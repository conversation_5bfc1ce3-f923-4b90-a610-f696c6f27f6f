import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime
import os

# pyperclip będzie zaimportowany w funkcji main() z obsługą błędów

class PercentCalculator:
    def __init__(self, root, pyperclip=None):
        self.root = root
        self.pyperclip = pyperclip
        # Style dla entry
        self.style = ttk.Style()
        self.style.configure("White.TEntry", fieldbackground="white")
        self.style.configure("Error.TEntry", fieldbackground="#ffcccc")
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.setup_bindings()
        
    def setup_window(self):
        self.root.title("Kalkulator Procentowy Professional")
        self.root.geometry("700x600")
        self.root.minsize(600, 500)
        
        # Ikona okna - użyj icon.ico z katalogu aplikacji
        try:
            # Sprawdź czy plik icon.ico istnieje w katalogu aplikacji
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
            elif os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
            else:
                print("Uwaga: Plik icon.ico nie został znaleziony.")
        except Exception as e:
            print(f"Nie można załadować ikony: {e}")
            pass
            
    def setup_variables(self):
        self.calculation_type = tk.StringVar(value="x% z liczby y")
        self.value1 = tk.StringVar()
        self.value2 = tk.StringVar()
        self.value3 = tk.StringVar()
        self.result = tk.StringVar()
        self.history = []
        
        # Typy obliczeń
        self.calc_types = {
            "x% z liczby y": {"fields": 2, "labels": ["Procent (%):", "Liczba:"], "desc": "Oblicza wartość procentu z podanej liczby"},
            "Ile % stanowi x z y": {"fields": 2, "labels": ["Wartość:", "Z liczby:"], "desc": "Oblicza jaki procent stanowi jedna liczba z drugiej"},
            "Wzrost/spadek procentowy": {"fields": 2, "labels": ["Wartość początkowa:", "Wartość końcowa:"], "desc": "Oblicza procentową zmianę między wartościami"},
            "Dodaj procent do liczby": {"fields": 2, "labels": ["Liczba:", "Procent (%):"], "desc": "Dodaje procent do liczby (np. podatek)"},
            "Odejmij procent od liczby": {"fields": 2, "labels": ["Liczba:", "Procent (%):"], "desc": "Odejmuje procent od liczby (np. rabat)"},
            "Znajdź liczbę bazową": {"fields": 2, "labels": ["Wartość końcowa:", "Procent (%):"], "desc": "Znajduje liczbę bazową dla danej wartości i procentu"}
        }
        
    def create_widgets(self):
        self.create_menu()
        self.create_main_frame()
        self.create_calculator_section()
        self.create_history_section()
        self.create_status_bar()
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Plik
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Plik", menu=file_menu)
        file_menu.add_command(label="Eksportuj historię", command=self.export_history)
        file_menu.add_command(label="Importuj historię", command=self.import_history)
        file_menu.add_separator()
        file_menu.add_command(label="Wyjście", command=self.root.quit)
        
        # Menu Narzędzia
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Narzędzia", menu=tools_menu)
        tools_menu.add_command(label="Wyczyść wszystko", command=self.clear_all)
        tools_menu.add_command(label="Wyczyść historię", command=self.clear_history)
        
        # Menu Pomoc
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Pomoc", menu=help_menu)
        help_menu.add_command(label="Instrukcja", command=self.show_help)
        help_menu.add_command(label="O programie", command=self.show_about)
        
    def create_main_frame(self):
        # Główna ramka z podziałem na kalulator i historię
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Lewa strona - kalkulator
        self.calc_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.calc_frame, weight=2)
        
        # Prawa strona - historia
        self.history_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.history_frame, weight=1)
        
    def create_calculator_section(self):
        # Tytuł sekcji
        calc_title = ttk.Label(self.calc_frame, text="Kalkulator", font=("Arial", 14, "bold"))
        calc_title.pack(pady=(0, 10))
        
        # Wybór typu obliczenia
        type_frame = ttk.LabelFrame(self.calc_frame, text="Typ obliczenia", padding=10)
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.type_combo = ttk.Combobox(type_frame, textvariable=self.calculation_type,
                                      values=list(self.calc_types.keys()), state="readonly", width=30)
        self.type_combo.pack()
        self.type_combo.bind("<<ComboboxSelected>>", self.on_type_change)
        
        # Opis typu obliczenia
        self.desc_label = ttk.Label(type_frame, text="", foreground="blue", font=("Arial", 9))
        self.desc_label.pack(pady=(5, 0))
        
        # Pola wprowadzania
        self.input_frame = ttk.LabelFrame(self.calc_frame, text="Dane wejściowe", padding=10)
        self.input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Dynamiczne pola
        self.label1 = ttk.Label(self.input_frame, text="Wartość 1:")
        self.label1.grid(row=0, column=0, sticky=tk.W, pady=2)
        self.entry1 = ttk.Entry(self.input_frame, textvariable=self.value1, width=20)
        self.entry1.grid(row=0, column=1, padx=(10, 0), pady=2)
        self.entry1.bind("<KeyRelease>", self.validate_input)
        
        self.label2 = ttk.Label(self.input_frame, text="Wartość 2:")
        self.label2.grid(row=1, column=0, sticky=tk.W, pady=2)
        self.entry2 = ttk.Entry(self.input_frame, textvariable=self.value2, width=20)
        self.entry2.grid(row=1, column=1, padx=(10, 0), pady=2)
        self.entry2.bind("<KeyRelease>", self.validate_input)
        
        self.label3 = ttk.Label(self.input_frame, text="Wartość 3:")
        self.label3.grid(row=2, column=0, sticky=tk.W, pady=2)
        self.entry3 = ttk.Entry(self.input_frame, textvariable=self.value3, width=20)
        self.entry3.grid(row=2, column=1, padx=(10, 0), pady=2)
        self.entry3.bind("<KeyRelease>", self.validate_input)
        
        # Przyciski
        button_frame = ttk.Frame(self.calc_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.calc_button = ttk.Button(button_frame, text="Oblicz", command=self.calculate)
        self.calc_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_button = ttk.Button(button_frame, text="Wyczyść", command=self.clear_fields)
        self.clear_button.pack(side=tk.LEFT)
        
        # Wynik
        result_frame = ttk.LabelFrame(self.calc_frame, text="Wynik", padding=10)
        result_frame.pack(fill=tk.X)
        
        self.result_text = tk.Text(result_frame, height=4, width=40, state=tk.DISABLED,
                                  bg="#f0f0f0", font=("Arial", 11))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        copy_button = ttk.Button(result_frame, text="Kopiuj wynik", command=self.copy_result)
        copy_button.pack(pady=(5, 0))
        
        # Inicjalizacja pól
        self.on_type_change()
        
    def create_history_section(self):
        # Tytuł
        hist_title = ttk.Label(self.history_frame, text="Historia obliczeń", font=("Arial", 14, "bold"))
        hist_title.pack(pady=(0, 10))
        
        # Lista historii
        history_list_frame = ttk.Frame(self.history_frame)
        history_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.history_listbox = tk.Listbox(history_list_frame, font=("Courier", 9))
        scrollbar = ttk.Scrollbar(history_list_frame, orient=tk.VERTICAL, command=self.history_listbox.yview)
        self.history_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Przyciski historii
        hist_buttons = ttk.Frame(self.history_frame)
        hist_buttons.pack(fill=tk.X)
        
        ttk.Button(hist_buttons, text="Użyj ponownie", command=self.reuse_calculation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_buttons, text="Wyczyść", command=self.clear_history).pack(side=tk.LEFT)
        
    def create_status_bar(self):
        self.status_bar = ttk.Label(self.root, text="Gotowy", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def setup_bindings(self):
        # Skróty klawiszowe
        self.root.bind("<Return>", lambda e: self.calculate())
        self.root.bind("<Control-c>", lambda e: self.copy_result())
        self.root.bind("<Escape>", lambda e: self.clear_fields())
        
    def on_type_change(self, event=None):
        calc_type = self.calculation_type.get()
        config = self.calc_types[calc_type]
        
        # Aktualizuj opis
        self.desc_label.config(text=config["desc"])
        
        # Aktualizuj etykiety i widoczność pól
        labels = config["labels"]
        fields_count = config["fields"]
        
        # Ukryj wszystkie pola
        self.label3.grid_remove()
        self.entry3.grid_remove()
        
        # Pokaż i zaktualizuj potrzebne pola
        self.label1.config(text=labels[0])
        self.label2.config(text=labels[1])
        
        if fields_count > 2:
            self.label3.config(text=labels[2])
            self.label3.grid()
            self.entry3.grid()
            
        # Wyczyść pola
        self.clear_fields()
        
    def validate_input(self, event=None):
        # Walidacja w czasie rzeczywistym
        widget = event.widget
        text = widget.get()
        
        if text:
            # Zamień przecinek na kropkę
            text = text.replace(',', '.')
            try:
                float(text)
                widget.configure(style="White.TEntry")
            except ValueError:
                widget.configure(style="Error.TEntry")
        else:
            widget.configure(style="White.TEntry")
            
    def is_valid_number(self, value_str):
        if not value_str.strip():
            return False
        try:
            float(value_str.replace(',', '.'))
            return True
        except ValueError:
            return False
            
    def get_number(self, value_str):
        return float(value_str.replace(',', '.'))
        
    def calculate(self):
        try:
            calc_type = self.calculation_type.get()
            
            # Sprawdź czy potrzebne pola są wypełnione
            val1 = self.value1.get().strip()
            val2 = self.value2.get().strip()
            
            if not self.is_valid_number(val1) or not self.is_valid_number(val2):
                self.show_error("Wprowadź prawidłowe liczby w pierwszych dwóch polach!")
                return
                
            num1 = self.get_number(val1)
            num2 = self.get_number(val2)
            
            result = None
            result_text = ""
            
            if calc_type == "x% z liczby y":
                if num1 < 0:
                    self.show_error("Procent nie może być ujemny!")
                    return
                result = (num1 / 100) * num2
                result_text = f"{num1}% z {num2} = {result:.2f}"
                
            elif calc_type == "Ile % stanowi x z y":
                if num2 == 0:
                    self.show_error("Nie można dzielić przez zero!")
                    return
                result = (num1 / num2) * 100
                result_text = f"{num1} stanowi {result:.2f}% z {num2}"
                
            elif calc_type == "Wzrost/spadek procentowy":
                if num1 == 0:
                    self.show_error("Wartość początkowa nie może być zerem!")
                    return
                result = ((num2 - num1) / num1) * 100
                change_type = "wzrost" if result >= 0 else "spadek"
                result_text = f"{change_type.capitalize()} o {abs(result):.2f}%\nZ {num1} na {num2}"
                
            elif calc_type == "Dodaj procent do liczby":
                if num2 < 0:
                    self.show_error("Procent nie może być ujemny!")
                    return
                result = num1 * (1 + num2 / 100)
                result_text = f"{num1} + {num2}% = {result:.2f}"
                
            elif calc_type == "Odejmij procent od liczby":
                if num2 < 0:
                    self.show_error("Procent nie może być ujemny!")
                    return
                if num2 > 100:
                    self.show_error("Nie można odjąć więcej niż 100%!")
                    return
                result = num1 * (1 - num2 / 100)
                result_text = f"{num1} - {num2}% = {result:.2f}"
                
            elif calc_type == "Znajdź liczbę bazową":
                if num2 == 0:
                    self.show_error("Procent nie może być zerem!")
                    return
                result = (num1 * 100) / num2
                result_text = f"Liczba bazowa: {result:.2f}\n{num2}% z {result:.2f} = {num1}"
                
            # Wyświetl wynik
            self.display_result(result_text)
            
            # Dodaj do historii
            timestamp = datetime.now().strftime("%H:%M:%S")
            history_entry = f"[{timestamp}] {calc_type}: {result_text.replace(chr(10), ' | ')}"
            self.add_to_history(history_entry)
            
            self.update_status("Obliczenie wykonane pomyślnie")
            
        except Exception as e:
            self.show_error(f"Błąd obliczenia: {str(e)}")
            
    def display_result(self, text):
        self.result_text.config(state=tk.NORMAL)
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(1.0, text)
        self.result_text.config(state=tk.DISABLED)
        
    def copy_result(self):
        result = self.result_text.get(1.0, tk.END).strip()
        if result:
            try:
                if self.pyperclip:
                    self.pyperclip.copy(result)
                    self.update_status("Wynik skopiowany do schowka")
                else:
                    raise Exception("pyperclip not available")
            except:
                # Fallback jeśli pyperclip nie działa
                self.root.clipboard_clear()
                self.root.clipboard_append(result)
                self.update_status("Wynik skopiowany do schowka (fallback)")
        else:
            self.show_error("Brak wyniku do skopiowania!")
            
    def add_to_history(self, entry):
        self.history.append(entry)
        self.history_listbox.insert(tk.END, entry)
        self.history_listbox.see(tk.END)
        
        # Ogranicz historię do 100 elementów
        if len(self.history) > 100:
            self.history.pop(0)
            self.history_listbox.delete(0)
            
    def reuse_calculation(self):
        selection = self.history_listbox.curselection()
        if not selection:
            self.show_error("Wybierz obliczenie z historii!")
            return
            
        # Tu można zaimplementować parsowanie historii i przywrócenie wartości
        self.update_status("Funkcja w rozwoju...")
        
    def clear_fields(self):
    self.value1.set("")
    self.value2.set("")
    self.value3.set("")
    self.display_result("")
    self.entry1.configure(style="White.TEntry")
    self.entry2.configure(style="White.TEntry")
    self.entry3.configure(style="White.TEntry")
    self.update_status("Pola wyczyszczone")
        
    def clear_history(self):
        self.history.clear()
        self.history_listbox.delete(0, tk.END)
        self.update_status("Historia wyczyszczona")
        
    def clear_all(self):
        self.clear_fields()
        self.clear_history()
        
    def export_history(self):
        if not self.history:
            self.show_error("Historia jest pusta!")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Pliki tekstowe", "*.txt"), ("Wszystkie pliki", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("Historia obliczeń procentowych\n")
                    f.write("=" * 40 + "\n\n")
                    for entry in self.history:
                        f.write(entry + "\n")
                self.update_status(f"Historia wyeksportowana do {filename}")
            except Exception as e:
                self.show_error(f"Błąd eksportu: {str(e)}")
                
    def import_history(self):
        filename = filedialog.askopenfilename(
            filetypes=[("Pliki tekstowe", "*.txt"), ("Wszystkie pliki", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                imported_count = 0
                for line in lines:
                    line = line.strip()
                    if line and line.startswith('['):
                        self.add_to_history(line)
                        imported_count += 1
                        
                self.update_status(f"Zaimportowano {imported_count} wpisów")
            except Exception as e:
                self.show_error(f"Błąd importu: {str(e)}")
                
    def show_help(self):
        help_text = """
Instrukcja Kalkulatora Procentowego

Typy obliczeń:
• x% z liczby y - oblicza wartość procentu
• Ile % stanowi x z y - oblicza procentowy udział
• Wzrost/spadek procentowy - porównuje dwie wartości
• Dodaj/Odejmij procent - oblicza z podatkiem/rabatem
• Znajdź liczbę bazową - odwrotne obliczenie

Skróty klawiszowe:
• Enter - Oblicz
• Ctrl+C - Kopiuj wynik
• Escape - Wyczyść pola

Wskazówki:
• Można używać przecinka i kropki jako separator dziesiętny
• Historia obliczeń jest automatycznie zapisywana
• Wyniki można eksportować do pliku
        """
        
        messagebox.showinfo("Instrukcja", help_text.strip())
        
    def show_about(self):
        about_text = """Kalkulator Procentowy Professional v1.0

Zaawansowana aplikacja do obliczeń procentowych
z historią, eksportem i walidacją danych.

Autor: Python Developer
Data: 2025"""
        
        messagebox.showinfo("O programie", about_text)
        
    def show_error(self, message):
        messagebox.showerror("Błąd", message)
        
    def update_status(self, message):
        self.status_bar.config(text=message)
        self.root.after(3000, lambda: self.status_bar.config(text="Gotowy"))

def main():
    # Sprawdź czy pyperclip jest dostępne
    try:
        import pyperclip
    except ImportError:
        print("Uwaga: Biblioteka pyperclip nie jest zainstalowana.")
        print("Kopiowanie do schowka będzie działać w trybie fallback.")
        print("Aby zainstalować: pip install pyperclip")
        
        # Stwórz zastępczą klasę
        class MockPyperclip:
            @staticmethod
            def copy(text):
                raise Exception("pyperclip not available")
        
        pyperclip = MockPyperclip()
    
    root = tk.Tk()
    app = PercentCalculator(root, pyperclip=pyperclip)
    root.mainloop()

if __name__ == "__main__":
    main()